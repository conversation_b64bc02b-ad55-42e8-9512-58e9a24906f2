import 'package:flutter_test/flutter_test.dart';
import 'package:dio/dio.dart';
import 'dart:convert';

void main() {
  group('实际服务器测试', () {
    test('测试您提供的具体URL', () async {
      print('=== 测试实际服务器响应 ===');
      
      // 使用您提供的URL，但更新时间戳
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final url = 'https://vplates.com.au/vplatesapi/checkcombo';
      final params = {
        'vehicleType': 'car', // 注意：您的URL中使用的是小写
        'combination': 'ylh22t',
        'productType': 'Create',
        'isRestyle': 'false',
        '_': timestamp.toString(),
      };
      
      // 使用您在浏览器中看到的header信息
      final headers = {
        'Accept': '*/*',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Accept-Language': 'zh-CN,zh;q=0.9,en-AU;q=0.8,en;q=0.7',
        'DNT': '1',
        'Priority': 'u=1, i',
        'Sec-CH-UA': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'Sec-CH-UA-Mobile': '?0',
        'Sec-CH-UA-Platform': '"macOS"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'X-NewRelic-ID': 'VQIPU1dVDhACUVRaDwgGUlI=',
        'Referer': 'https://vplates.com.au/create/check-combination',
        'X-Requested-With': 'XMLHttpRequest',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
      };
      
      final queryString = params.entries
          .map((e) => '${e.key}=${e.value}')
          .join('&');
      final fullUrl = '$url?$queryString';
      
      print('完整URL: $fullUrl');
      print('时间戳: $timestamp');
      
      final dio = Dio();
      
      try {
        print('\n=== 发送请求 ===');
        final response = await dio.get(
          url,
          queryParameters: params,
          options: Options(
            headers: headers,
            validateStatus: (status) => true, // 接受所有状态码
            followRedirects: true,
            maxRedirects: 5,
          ),
        );
        
        print('\n=== 响应信息 ===');
        print('状态码: ${response.statusCode}');
        print('响应头:');
        response.headers.forEach((key, values) {
          print('  $key: ${values.join(', ')}');
        });
        
        print('\n=== 响应数据分析 ===');
        final data = response.data;
        print('数据类型: ${data.runtimeType}');
        
        if (data is String) {
          print('响应长度: ${data.length} 字符');
          
          // 检查内容编码
          final contentEncoding = response.headers.value('content-encoding');
          print('内容编码: $contentEncoding');
          
          // 检查是否是压缩的内容（乱码）
          final isCompressed = contentEncoding != null && 
                              (contentEncoding.contains('gzip') || 
                               contentEncoding.contains('br') || 
                               contentEncoding.contains('deflate'));
          
          if (isCompressed) {
            print('✅ 检测到压缩内容，这解释了为什么看到乱码');
            print('内容编码方式: $contentEncoding');
          }
          
          // 尝试检测是否是HTML（即使被压缩）
          final contentType = response.headers.value('content-type');
          print('内容类型: $contentType');
          
          // 检查Cloudflare标识
          final server = response.headers.value('server');
          final cfMitigated = response.headers.value('cf-mitigated');
          final cfRay = response.headers.value('cf-ray');
          
          print('\n=== Cloudflare检测 ===');
          print('Server: $server');
          print('CF-Mitigated: $cfMitigated');
          print('CF-Ray: $cfRay');
          
          if (server == 'cloudflare' || cfMitigated != null || cfRay != null) {
            print('✅ 确认这是Cloudflare保护的响应');
            
            if (response.statusCode == 403) {
              print('✅ 403状态码确认被Cloudflare阻止');
              
              if (cfMitigated == 'challenge') {
                print('✅ cf-mitigated: challenge 表明这是挑战页面');
              }
            }
          }
          
          // 显示原始数据的前200字符（即使是乱码）
          print('\n=== 原始响应内容（前200字符）===');
          final preview = data.length > 200 ? data.substring(0, 200) : data;
          print('原始内容: $preview');
          
          // 尝试检测是否包含HTML标签（在压缩前）
          if (contentType?.contains('text/html') == true) {
            print('✅ Content-Type表明这是HTML响应');
            print('这很可能是Cloudflare挑战页面，但被压缩了');
          }
          
        } else {
          print('响应数据: $data');
        }
        
        // 检查Set-Cookie
        final setCookies = response.headers['set-cookie'];
        if (setCookies != null && setCookies.isNotEmpty) {
          print('\n=== Set-Cookie分析 ===');
          for (final cookie in setCookies) {
            print('Cookie: $cookie');
            
            if (cookie.contains('__cf_bm')) {
              print('  ✅ 检测到Cloudflare Bot Management Cookie');
            }
            if (cookie.contains('cf_clearance')) {
              print('  ✅ 检测到Cloudflare Clearance Cookie');
            }
          }
        }
        
      } catch (e) {
        print('\n=== 请求异常 ===');
        print('异常类型: ${e.runtimeType}');
        print('异常信息: $e');
        
        if (e is DioException) {
          print('DioException详情:');
          print('  类型: ${e.type}');
          print('  消息: ${e.message}');
          print('  状态码: ${e.response?.statusCode}');
          
          if (e.response?.data != null) {
            final responseData = e.response!.data.toString();
            print('  响应数据长度: ${responseData.length}');
            
            if (responseData.length > 0) {
              final preview = responseData.length > 500 ? responseData.substring(0, 500) : responseData;
              print('  响应预览: $preview');
            }
          }
        }
      }
    });

    test('测试不同的vehicleType格式', () async {
      print('\n=== 测试vehicleType参数格式 ===');
      
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final baseUrl = 'https://vplates.com.au/vplatesapi/checkcombo';
      
      // 测试小写和大写
      final testCases = [
        {'vehicleType': 'car', 'description': '小写car（您URL中的格式）'},
        {'vehicleType': 'CAR', 'description': '大写CAR（我们之前使用的格式）'},
      ];
      
      for (final testCase in testCases) {
        print('\n--- 测试 ${testCase['description']} ---');
        
        final params = {
          'vehicleType': testCase['vehicleType'],
          'combination': 'test123',
          'productType': 'Create',
          'isRestyle': 'false',
          '_': timestamp.toString(),
        };
        
        final headers = {
          'Accept': '*/*',
          'Accept-Encoding': 'gzip, deflate, br, zstd',
          'Accept-Language': 'zh-CN,zh;q=0.9,en-AU;q=0.8,en;q=0.7',
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
          'Referer': 'https://vplates.com.au/create/check-combination',
          'X-Requested-With': 'XMLHttpRequest',
        };
        
        final dio = Dio();
        
        try {
          final response = await dio.get(
            baseUrl,
            queryParameters: params,
            options: Options(
              headers: headers,
              validateStatus: (status) => true,
            ),
          );
          
          print('状态码: ${response.statusCode}');
          print('Server: ${response.headers.value('server')}');
          print('CF-Mitigated: ${response.headers.value('cf-mitigated')}');
          
          if (response.statusCode == 403) {
            print('❌ 仍然收到403错误');
          } else {
            print('✅ 状态码正常: ${response.statusCode}');
          }
          
        } catch (e) {
          print('❌ 请求失败: $e');
        }
        
        // 添加延迟避免请求过快
        await Future.delayed(const Duration(seconds: 2));
      }
    });

    test('验证我们的检测逻辑', () {
      print('\n=== 验证Cloudflare检测逻辑 ===');
      
      // 模拟典型的Cloudflare 403响应
      final mockHeaders = {
        'server': ['cloudflare'],
        'cf-mitigated': ['challenge'],
        'cf-ray': ['95964ed8b9ade7ca-SYD'],
        'content-type': ['text/html; charset=UTF-8'],
        'content-encoding': ['br'],
      };
      
      final statusCode = 403;
      final contentType = mockHeaders['content-type']?[0];
      final server = mockHeaders['server']?[0];
      final cfMitigated = mockHeaders['cf-mitigated']?[0];
      
      print('模拟响应:');
      print('  状态码: $statusCode');
      print('  Content-Type: $contentType');
      print('  Server: $server');
      print('  CF-Mitigated: $cfMitigated');
      
      // 我们的检测逻辑
      final isCloudflareChallenge = (statusCode == 403) &&
                                   (server == 'cloudflare' || cfMitigated == 'challenge' || 
                                    contentType?.contains('text/html') == true);
      
      print('\n检测结果:');
      print('  是否为Cloudflare挑战: $isCloudflareChallenge');
      
      expect(isCloudflareChallenge, isTrue);
      print('✅ 检测逻辑正确');
    });
  });
}
