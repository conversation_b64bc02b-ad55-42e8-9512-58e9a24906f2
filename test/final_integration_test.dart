import 'package:flutter_test/flutter_test.dart';
import 'package:myplates_vic/services/api_service.dart';
import 'package:myplates_vic/services/browser_headers.dart';
import 'package:myplates_vic/models/plate_number.dart';
import 'package:myplates_vic/enums/vehicle_type.dart';

void main() {
  group('最终集成测试', () {
    late ApiService apiService;

    setUpAll(() async {
      apiService = ApiService();
      await apiService.initialize();
      apiService.enableWebViewFallback();
    });

    test('完整的反Cloudflare机器人检测测试', () async {
      print('=== 完整的反Cloudflare机器人检测测试 ===');
      
      final testPlate = PlateNumber(
        number: 'ylh22t', // 使用您提供的测试车牌号
        type: 'Personalised',
      );

      print('测试车牌: ${testPlate.number}');
      print('WebView回退已启用: ${apiService.isWebViewFallbackEnabled}');
      
      // 验证请求头配置
      final headers = await BrowserHeaders.instance.getBrowserHeaders(
        referer: 'https://vplates.com.au/create/check-combination',
      );
      
      print('\n=== 验证请求头配置 ===');
      print('Accept-Language: ${headers['Accept-Language']}');
      print('Accept-Encoding: ${headers['Accept-Encoding']}');
      print('User-Agent: ${headers['User-Agent']}');
      
      // 验证语言设置是否正确
      expect(headers['Accept-Language'], contains('zh-CN'));
      expect(headers['Accept-Language'], contains('en-AU'));
      print('✅ 语言设置正确');
      
      // 验证编码设置
      expect(headers['Accept-Encoding'], contains('br'));
      expect(headers['Accept-Encoding'], contains('zstd'));
      print('✅ 编码设置正确');
      
      // 验证vehicleType参数格式
      print('\n=== 验证vehicleType参数格式 ===');
      print('VehicleType.car.apiValue: ${VehicleType.car.apiValue}');
      print('小写格式: ${VehicleType.car.apiValue.toLowerCase()}');
      expect(VehicleType.car.apiValue.toLowerCase(), equals('car'));
      print('✅ vehicleType参数格式正确');
      
      print('\n=== 开始API请求测试 ===');
      
      bool cloudflareDetected = false;
      String detectionDetails = '';
      
      try {
        final results = await apiService.checkPlatesAvailability(
          plates: [testPlate],
          concurrency: 1,
          vehicleType: VehicleType.car,
          onProgress: (completed, total) {
            print('查询进度: $completed/$total');
          },
        );

        print('API请求成功完成');
        print('车牌号: ${results.first.number}');
        print('是否可用: ${results.first.isAvailable}');
        print('查询时间: ${results.first.queryTime}');
        
        if (results.first.isAvailable == null) {
          print('⚠️  返回结果为null，可能表示服务器响应异常');
        }
        
      } catch (e) {
        print('API请求被阻止: $e');
        
        // 分析异常类型
        final errorMessage = e.toString().toLowerCase();
        
        if (errorMessage.contains('cloudflare') && errorMessage.contains('挑战')) {
          cloudflareDetected = true;
          detectionDetails = '✅ 正确检测到Cloudflare挑战（基于异常消息）';
        } else if (errorMessage.contains('403')) {
          cloudflareDetected = true;
          detectionDetails = '✅ 检测到403状态码，表明被Cloudflare阻止';
        } else if (errorMessage.contains('html')) {
          cloudflareDetected = true;
          detectionDetails = '✅ 检测到HTML响应，可能是挑战页面';
        } else {
          detectionDetails = '❓ 其他类型的错误: $e';
        }
      }
      
      print('\n=== 检测结果分析 ===');
      print('Cloudflare挑战检测: ${cloudflareDetected ? "成功" : "未检测到"}');
      print('检测详情: $detectionDetails');
      
      // 检查Cookie状态
      final cookieStats = await apiService.getCookieStats();
      print('Cookie统计: $cookieStats');
      
      // 检查行为统计
      final behaviorStats = apiService.getBehaviorStats();
      print('行为统计: $behaviorStats');
      
      print('\n=== 测试总结 ===');
      print('1. 请求头配置: ✅ 正确');
      print('2. 参数格式: ✅ 正确');
      print('3. Cloudflare检测: ${cloudflareDetected ? "✅ 正常工作" : "⚠️  未触发"}');
      print('4. WebView回退: ✅ 已启用');
      print('5. Cookie管理: ✅ 正常工作');
      
      // 在实际应用中，如果检测到Cloudflare挑战，应该显示WebView
      if (cloudflareDetected) {
        print('\n💡 在实际应用中的处理流程:');
        print('1. 检测到Cloudflare挑战');
        print('2. 显示WebView验证页面');
        print('3. 用户完成验证');
        print('4. 提取验证后的Cookie');
        print('5. 重新尝试API请求');
      }
    });

    test('验证WebView回退机制', () async {
      print('\n=== 验证WebView回退机制 ===');
      
      // 测试启用/禁用功能
      expect(apiService.isWebViewFallbackEnabled, isTrue);
      print('✅ WebView回退已启用');
      
      apiService.disableWebViewFallback();
      expect(apiService.isWebViewFallbackEnabled, isFalse);
      print('✅ WebView回退可以正确禁用');
      
      apiService.enableWebViewFallback();
      expect(apiService.isWebViewFallbackEnabled, isTrue);
      print('✅ WebView回退可以重新启用');
      
      // 测试在禁用WebView回退时的行为
      apiService.disableWebViewFallback();
      
      final testPlate = PlateNumber(
        number: 'test456',
        type: 'Personalised',
      );
      
      try {
        await apiService.checkPlatesAvailability(
          plates: [testPlate],
          concurrency: 1,
          vehicleType: VehicleType.car,
          onProgress: (completed, total) {
            print('进度: $completed/$total');
          },
        );
        print('请求成功（WebView回退已禁用）');
      } catch (e) {
        final errorMessage = e.toString();
        if (errorMessage.contains('WebView回退未启用')) {
          print('✅ 正确处理了WebView回退禁用的情况');
        } else {
          print('收到其他错误: $e');
        }
      }
      
      // 恢复WebView回退
      apiService.enableWebViewFallback();
    });

    test('验证人性化行为模拟', () async {
      print('\n=== 验证人性化行为模拟 ===');
      
      final testPlates = [
        PlateNumber(number: 'test1', type: 'Personalised'),
        PlateNumber(number: 'test2', type: 'Personalised'),
        PlateNumber(number: 'test3', type: 'Personalised'),
      ];
      
      final startTime = DateTime.now();
      
      try {
        await apiService.checkPlatesAvailability(
          plates: testPlates,
          concurrency: 1, // 低并发以触发延迟
          vehicleType: VehicleType.car,
          onProgress: (completed, total) {
            final elapsed = DateTime.now().difference(startTime);
            print('进度: $completed/$total (耗时: ${elapsed.inSeconds}秒)');
          },
        );
      } catch (e) {
        print('请求过程中的异常（预期）: ${e.toString().substring(0, 100)}...');
      }
      
      final totalTime = DateTime.now().difference(startTime);
      print('总耗时: ${totalTime.inSeconds}秒');
      
      // 验证是否有适当的延迟（人性化行为）
      if (totalTime.inSeconds >= 2) {
        print('✅ 检测到人性化延迟行为');
      } else {
        print('⚠️  延迟时间较短，可能需要调整');
      }
      
      // 检查行为统计
      final behaviorStats = apiService.getBehaviorStats();
      print('行为统计: $behaviorStats');
    });

    tearDownAll(() async {
      // 清理测试数据
      await apiService.clearCookies();
      apiService.disableWebViewFallback();
      BrowserHeaders.instance.clearCache();
      print('\n=== 测试清理完成 ===');
    });
  });

  group('实际使用场景验证', () {
    test('模拟真实用户查询流程', () async {
      print('\n=== 模拟真实用户查询流程 ===');
      
      final apiService = ApiService();
      await apiService.initialize();
      apiService.enableWebViewFallback();
      
      // 模拟用户输入的车牌号
      final userPlates = [
        PlateNumber(number: 'ABC123', type: 'Personalised'),
        PlateNumber(number: 'XYZ789', type: 'Personalised'),
      ];
      
      print('用户查询车牌: ${userPlates.map((p) => p.number).join(', ')}');
      
      for (int attempt = 1; attempt <= 2; attempt++) {
        print('\n--- 第$attempt次尝试 ---');
        
        try {
          final results = await apiService.checkPlatesAvailability(
            plates: userPlates,
            concurrency: 1,
            vehicleType: VehicleType.car,
            onProgress: (completed, total) {
              print('  查询进度: $completed/$total');
            },
          );
          
          print('✅ 查询成功完成');
          for (final result in results) {
            print('  ${result.number}: ${result.isAvailable ?? "未知"}');
          }
          break;
          
        } catch (e) {
          print('❌ 查询失败: $e');
          
          if (e.toString().contains('Cloudflare') || e.toString().contains('挑战')) {
            print('💡 在实际应用中，此时会显示WebView验证页面');
            print('💡 用户完成验证后，会重新尝试查询');
            
            // 模拟验证成功后的重试
            if (attempt == 1) {
              print('💡 模拟验证成功，准备重试...');
              await Future.delayed(const Duration(seconds: 1));
            }
          } else {
            print('💡 其他类型的错误，可能需要用户检查网络连接');
            break;
          }
        }
      }
      
      await apiService.clearCookies();
    });
  });
}
