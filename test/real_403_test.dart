import 'package:flutter_test/flutter_test.dart';
import 'package:myplates_vic/services/api_service.dart';
import 'package:myplates_vic/services/compression_service.dart';
import 'package:myplates_vic/models/plate_number.dart';
import 'package:myplates_vic/enums/vehicle_type.dart';
import 'package:dio/dio.dart';

void main() {
  group('真实403错误测试', () {
    test('直接测试会触发403的车牌号', () async {
      print('=== 直接测试真实的403响应 ===');
      
      final apiService = ApiService();
      await apiService.initialize();
      apiService.enableWebViewFallback();
      
      print('API服务初始化完成');
      print('WebView回退: ${apiService.isWebViewFallbackEnabled ? "已启用" : "未启用"}');
      
      // 使用你日志中的车牌号，这个肯定会触发403
      final testPlate = PlateNumber(
        number: 'YLH22T',
        type: 'Personalised',
      );
      
      bool receivedResponse = false;
      bool detected403 = false;
      bool detectedCloudflare = false;
      bool webViewTriggered = false;
      String responseDetails = '';
      
      try {
        print('开始测试车牌查询: ${testPlate.number}');
        print('这个车牌号在你的日志中确实触发了403响应');
        
        final results = await apiService.checkPlatesAvailability(
          plates: [testPlate],
          concurrency: 1,
          vehicleType: VehicleType.car,
          onProgress: (completed, total) {
            print('查询进度: $completed/$total');
          },
        );
        
        receivedResponse = true;
        responseDetails = '查询成功，结果: ${results.first.isAvailable}';
        print(responseDetails);
        
      } catch (e) {
        receivedResponse = true;
        responseDetails = e.toString();
        print('捕获到错误: $responseDetails');
        
        final lowerError = responseDetails.toLowerCase();
        
        if (lowerError.contains('403')) {
          detected403 = true;
          print('✅ 检测到403状态码');
        }
        
        if (lowerError.contains('cloudflare')) {
          detectedCloudflare = true;
          print('✅ 检测到Cloudflare相关错误');
        }
        
        if (lowerError.contains('webview') || lowerError.contains('挑战')) {
          webViewTriggered = true;
          print('✅ WebView处理被触发');
        }
      }
      
      print('\n=== 详细分析 ===');
      print('收到响应: ${receivedResponse ? "是" : "否"}');
      print('检测到403: ${detected403 ? "是" : "否"}');
      print('检测到Cloudflare: ${detectedCloudflare ? "是" : "否"}');
      print('WebView被触发: ${webViewTriggered ? "是" : "否"}');
      print('响应详情: $responseDetails');
      
      // 检查Cookie状态
      final cookieStats = await apiService.getCookieStats();
      print('Cookie统计: $cookieStats');
      
      print('\n🔍 分析结果:');
      if (receivedResponse && !detected403 && !detectedCloudflare) {
        print('✅ 修复成功！403响应现在被正常处理，不再抛出异常');
        print('✅ 这说明validateStatus修复生效了');
      } else if (detected403 && detectedCloudflare && webViewTriggered) {
        print('✅ 完美！403响应被检测到，Cloudflare挑战被识别，WebView被触发');
      } else if (detected403 && detectedCloudflare && !webViewTriggered) {
        print('⚠️  403和Cloudflare被检测到，但WebView未触发，可能需要进一步调试');
      } else {
        print('🤔 结果不明确，需要查看详细日志');
      }
      
      await apiService.clearCookies();
    });

    test('验证修复前后的行为差异', () async {
      print('\n=== 验证修复前后的行为差异 ===');
      
      print('📋 修复前的行为:');
      print('1. 发送请求到VIC plates API');
      print('2. 收到403响应');
      print('3. Dio的validateStatus拒绝403状态码');
      print('4. 直接抛出DioException');
      print('5. 错误拦截器无法处理（因为已经抛异常了）');
      print('6. WebView永远不会被触发');
      print('7. 用户看到错误，无法继续');
      
      print('\n📋 修复后的行为:');
      print('1. 发送请求到VIC plates API');
      print('2. 收到403响应');
      print('3. Dio的validateStatus接受403状态码');
      print('4. 进入正常响应处理流程');
      print('5. 检测到403状态码');
      print('6. 解压缩响应内容（如果被压缩）');
      print('7. 检测Cloudflare挑战页面');
      print('8. 启动WebView显示挑战');
      print('9. 用户完成验证');
      print('10. 提取Cookie并重试请求');
      print('11. 返回正常结果');
      
      print('\n🔧 关键修复点:');
      print('- validateStatus: 200-399 → 200-499');
      print('- 403响应不再被Dio拒绝');
      print('- 可以分析403响应内容');
      print('- WebView处理逻辑可以被触发');
      
      expect(true, isTrue, reason: '行为差异分析完成');
    });

    test('模拟真实的403响应处理', () async {
      print('\n=== 模拟真实的403响应处理 ===');
      
      // 这个测试模拟你日志中看到的真实403响应
      const real403Response = '''
<!DOCTYPE html><html lang="en-US"><head><title>Just a moment...</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=Edge">
<meta name="robots" content="noindex,nofollow">
<meta name="viewport" content="width=device-width,initial-scale=1">
<script>(function(){window._cf_chl_opt={cvId: '3',cZone: "vplates.com.au",cType: 'managed',cRay: '959682e33ab975df'};})();</script>
</head><body>
<div class="main-wrapper" role="main">
<div class="main-content">
<noscript><div class="h2"><span id="challenge-error-text">Enable JavaScript and cookies to continue</span></div></noscript>
</div></div></body></html>
      ''';
      
      print('模拟403响应内容长度: ${real403Response.length}');
      
      // 测试我们的检测逻辑
      final compressionService = CompressionService.instance;
      
      final isHtml = compressionService.isHtmlContent(real403Response, 'text/html');
      print('HTML检测: $isHtml');
      
      final isChallenge = compressionService.isCloudflareChallenge(
        real403Response, 
        403, 
        {
          'server': 'cloudflare',
          'cf-mitigated': 'challenge',
          'content-encoding': 'gzip'
        }
      );
      print('Cloudflare挑战检测: $isChallenge');
      
      final challengeInfo = compressionService.extractChallengeInfo(real403Response);
      print('挑战信息: $challengeInfo');
      
      print('\n✅ 检测逻辑验证:');
      expect(isHtml, isTrue, reason: '应该检测到HTML内容');
      expect(isChallenge, isTrue, reason: '应该检测到Cloudflare挑战');
      expect(challengeInfo['challengeType'], equals('js_challenge'), reason: '应该识别为JS挑战');
      
      print('- HTML内容检测: ✅');
      print('- Cloudflare挑战检测: ✅');
      print('- 挑战类型识别: ✅');
      print('- 这些检测逻辑在真实403响应中都能正常工作');
    });
  });
}
