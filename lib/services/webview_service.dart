import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:flutter/foundation.dart';
import '../utils/logger.dart';
import 'cookie_manager.dart';

/// WebView服务
/// 用于处理需要手动验证的情况，如CAPTCHA解决和浏览器验证
class WebViewService {
  static WebViewService? _instance;
  static WebViewService get instance => _instance ??= WebViewService._();
  
  WebViewController? _controller;
  bool _isInitialized = false;
  
  WebViewService._();
  
  /// 初始化WebView服务
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // WebView初始化（移除不支持的clearCookies调用）
      _isInitialized = true;
      Logger.instance.info('WebView服务初始化成功', 'WebViewService');
    } catch (e) {
      Logger.instance.error('WebView服务初始化失败: $e', 'WebViewService');
    }
  }
  
  /// 创建WebView控制器
  WebViewController createController({
    required String initialUrl,
    Function(String)? onPageFinished,
    Function(String)? onPageStarted,
    Function(WebResourceError)? onWebResourceError,
  }) {
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(const Color(0x00000000))
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            Logger.instance.debug('WebView开始加载: $url', 'WebViewService');
            onPageStarted?.call(url);
          },
          onPageFinished: (String url) async {
            Logger.instance.debug('WebView加载完成: $url', 'WebViewService');
            
            // 提取并保存Cookie
            await _extractAndSaveCookies(url);
            
            onPageFinished?.call(url);
          },
          onWebResourceError: (WebResourceError error) {
            Logger.instance.error('WebView资源错误: ${error.description}', 'WebViewService');
            onWebResourceError?.call(error);
          },
          onNavigationRequest: (NavigationRequest request) {
            Logger.instance.debug('WebView导航请求: ${request.url}', 'WebViewService');
            return NavigationDecision.navigate;
          },
        ),
      );
    
    // 设置用户代理
    _setUserAgent();
    
    // 加载初始URL
    _controller!.loadRequest(Uri.parse(initialUrl));
    
    return _controller!;
  }
  
  /// 设置用户代理
  Future<void> _setUserAgent() async {
    if (_controller == null) return;
    
    try {
      // 使用与HTTP请求相同的用户代理
      String userAgent;
      
      if (Platform.isAndroid) {
        userAgent = 'Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36';
      } else if (Platform.isIOS) {
        userAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1';
      } else {
        userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
      }
      
      await _controller!.setUserAgent(userAgent);
      Logger.instance.debug('WebView用户代理设置为: $userAgent', 'WebViewService');
    } catch (e) {
      Logger.instance.error('设置WebView用户代理失败: $e', 'WebViewService');
    }
  }
  
  /// 提取并保存Cookie（增强版）
  Future<void> _extractAndSaveCookies(String url) async {
    try {
      if (_controller == null) return;

      Logger.instance.debug('开始从WebView提取Cookie: $url', 'WebViewService');

      // 执行JavaScript获取Cookie
      final cookieString = await _controller!.runJavaScriptReturningResult(
        'document.cookie'
      ) as String;

      Logger.instance.debug('WebView Cookie字符串: $cookieString', 'WebViewService');

      if (cookieString.isNotEmpty && cookieString != 'null') {
        // 清理Cookie字符串（移除引号）
        final cleanCookieString = cookieString.replaceAll('"', '');
        Logger.instance.debug('清理后的Cookie字符串: $cleanCookieString', 'WebViewService');

        // 解析Cookie并保存到Cookie管理器
        final cookies = CookieManager.instance.parseCookieString(cleanCookieString);

        if (cookies.isNotEmpty) {
          final uri = Uri.parse(url);
          await CookieManager.instance.saveCookies(uri, cookies);
          Logger.instance.info('从WebView提取并保存了 ${cookies.length} 个Cookie', 'WebViewService');

          // 详细记录重要的Cookie
          for (final cookie in cookies) {
            if (cookie.name.startsWith('cf_') || cookie.name.startsWith('__cf_') ||
                cookie.name.contains('session') || cookie.name.contains('token')) {
              Logger.instance.info('重要Cookie: ${cookie.name} = ${cookie.value.length > 20 ? cookie.value.substring(0, 20) + "..." : cookie.value}', 'WebViewService');
            }
          }
        } else {
          Logger.instance.warning('解析Cookie失败，未找到有效Cookie', 'WebViewService');
        }
      } else {
        Logger.instance.warning('WebView中未找到Cookie', 'WebViewService');
      }
    } catch (e) {
      Logger.instance.error('提取WebView Cookie失败: $e', 'WebViewService');
    }
  }
  
  /// 注入Cookie到WebView
  Future<void> injectCookies(String url) async {
    try {
      if (_controller == null) return;
      
      final uri = Uri.parse(url);
      final cookies = await CookieManager.instance.getCookies(uri);
      
      if (cookies.isNotEmpty) {
        final cookieString = CookieManager.instance.cookiesToString(cookies);
        
        // 通过JavaScript设置Cookie
        await _controller!.runJavaScript(
          'document.cookie = "$cookieString";'
        );
        
        Logger.instance.info('向WebView注入了 ${cookies.length} 个Cookie', 'WebViewService');
      }
    } catch (e) {
      Logger.instance.error('向WebView注入Cookie失败: $e', 'WebViewService');
    }
  }
  
  /// 检查页面是否包含Cloudflare挑战（增强版）
  Future<bool> hasCloudflareChallenge() async {
    try {
      if (_controller == null) return false;

      Logger.instance.debug('开始检查Cloudflare挑战', 'WebViewService');

      // 获取当前URL
      final currentUrl = await _controller!.currentUrl();
      Logger.instance.debug('当前URL: $currentUrl', 'WebViewService');

      // 检查页面标题
      final title = await _controller!.getTitle();
      Logger.instance.debug('页面标题: $title', 'WebViewService');

      if (title != null) {
        final lowerTitle = title.toLowerCase();
        if (lowerTitle.contains('cloudflare') ||
            lowerTitle.contains('just a moment') ||
            lowerTitle.contains('checking your browser') ||
            lowerTitle.contains('ddos protection')) {
          Logger.instance.info('通过标题检测到Cloudflare挑战: $title', 'WebViewService');
          return true;
        }
      }

      // 检查页面内容中的Cloudflare标识（增强版）
      final challengeInfo = await _controller!.runJavaScriptReturningResult(
        '''
        (function() {
          var result = {
            hasChallenge: false,
            indicators: [],
            challengeType: 'unknown'
          };

          var body = document.body ? document.body.innerHTML.toLowerCase() : '';
          var html = document.documentElement ? document.documentElement.innerHTML.toLowerCase() : '';
          var content = body + ' ' + html;

          // 检查各种Cloudflare指标
          var indicators = [
            'cloudflare',
            'checking your browser',
            'please wait',
            'just a moment',
            'ddos protection',
            '_cf_chl_opt',
            'challenge-platform',
            'cf-browser-verification',
            'cf-wrapper',
            'cf-challenge',
            'cf-under-attack',
            'ray id'
          ];

          for (var i = 0; i < indicators.length; i++) {
            if (content.includes(indicators[i])) {
              result.hasChallenge = true;
              result.indicators.push(indicators[i]);
            }
          }

          // 检查特定的DOM元素
          if (document.querySelector('.cf-browser-verification') !== null ||
              document.querySelector('#cf-wrapper') !== null ||
              document.querySelector('.cf-challenge') !== null) {
            result.hasChallenge = true;
            result.indicators.push('dom_elements');
          }

          // 检查脚本内容
          var scripts = document.querySelectorAll('script');
          for (var i = 0; i < scripts.length; i++) {
            var scriptContent = scripts[i].innerHTML.toLowerCase();
            if (scriptContent.includes('_cf_chl_opt') ||
                scriptContent.includes('challenge-platform') ||
                scriptContent.includes('cloudflare')) {
              result.hasChallenge = true;
              result.indicators.push('script_content');
              break;
            }
          }

          // 确定挑战类型
          if (content.includes('checking your browser')) {
            result.challengeType = 'browser_check';
          } else if (content.includes('ddos protection')) {
            result.challengeType = 'ddos_protection';
          } else if (content.includes('just a moment')) {
            result.challengeType = 'js_challenge';
          } else if (content.includes('cf-under-attack')) {
            result.challengeType = 'under_attack';
          }

          return JSON.stringify(result);
        })();
        '''
      ) as String;

      Logger.instance.debug('挑战检测结果: $challengeInfo', 'WebViewService');

      // 解析结果
      if (challengeInfo.isNotEmpty && challengeInfo != 'null') {
        // 简单的JSON解析（避免依赖复杂的JSON库）
        final hasChallenge = challengeInfo.contains('"hasChallenge":true');

        if (hasChallenge) {
          Logger.instance.info('通过页面内容检测到Cloudflare挑战', 'WebViewService');
          Logger.instance.info('挑战详情: $challengeInfo', 'WebViewService');
          return true;
        }
      }

      Logger.instance.debug('未检测到Cloudflare挑战', 'WebViewService');
      return false;
    } catch (e) {
      Logger.instance.error('检查Cloudflare挑战失败: $e', 'WebViewService');
      return false;
    }
  }
  
  /// 等待Cloudflare验证完成（增强版）
  Future<bool> waitForCloudflareVerification({
    Duration timeout = const Duration(minutes: 3),
    Function(String)? onStatusUpdate,
  }) async {
    try {
      if (_controller == null) return false;

      final startTime = DateTime.now();
      int checkCount = 0;
      bool wasInChallenge = false;

      Logger.instance.info('=== 开始等待Cloudflare验证完成 ===', 'WebViewService');
      Logger.instance.info('超时时间: ${timeout.inMinutes}分钟', 'WebViewService');

      // 初始检查
      final initialChallenge = await hasCloudflareChallenge();
      Logger.instance.info('初始挑战状态: $initialChallenge', 'WebViewService');

      if (!initialChallenge) {
        Logger.instance.info('页面已经不在挑战状态，可能已经完成验证', 'WebViewService');
        return true;
      }

      while (DateTime.now().difference(startTime) < timeout) {
        checkCount++;
        final elapsed = DateTime.now().difference(startTime);

        // 检查是否仍在Cloudflare挑战页面
        final hasChallenge = await hasCloudflareChallenge();

        Logger.instance.debug('第${checkCount}次检查 (${elapsed.inSeconds}s)，仍有挑战: $hasChallenge', 'WebViewService');

        if (hasChallenge) {
          wasInChallenge = true;

          // 获取当前页面信息
          final currentUrl = await _controller!.currentUrl();
          final title = await _controller!.getTitle();
          Logger.instance.debug('挑战页面 - URL: $currentUrl, 标题: $title', 'WebViewService');

        } else if (wasInChallenge) {
          // 从挑战状态转为非挑战状态，说明验证完成
          Logger.instance.info('=== Cloudflare验证已完成！===', 'WebViewService');

          // 等待页面完全加载和稳定
          Logger.instance.info('等待页面稳定...', 'WebViewService');
          await Future.delayed(const Duration(seconds: 3));

          // 再次检查确保真的完成了
          final finalCheck = await hasCloudflareChallenge();
          if (finalCheck) {
            Logger.instance.warning('页面又回到挑战状态，继续等待', 'WebViewService');
            wasInChallenge = true;
            continue;
          }

          // 提取验证后的Cookie
          final currentUrl = await _controller!.currentUrl();
          Logger.instance.info('验证完成后的URL: $currentUrl', 'WebViewService');

          if (currentUrl != null) {
            Logger.instance.info('开始提取验证后的Cookie', 'WebViewService');
            await _extractAndSaveCookies(currentUrl);

            // 额外等待确保Cookie保存完成
            await Future.delayed(const Duration(milliseconds: 500));
          }

          Logger.instance.info('=== Cloudflare验证流程完成 ===', 'WebViewService');
          return true;
        }

        // 更新状态
        final remaining = timeout - elapsed;
        final statusMessage = hasChallenge
          ? '正在等待用户完成Cloudflare验证... (剩余${remaining.inSeconds}秒)'
          : '验证可能已完成，正在确认... (剩余${remaining.inSeconds}秒)';

        onStatusUpdate?.call(statusMessage);

        // 动态调整检查间隔
        final checkInterval = hasChallenge ? const Duration(seconds: 2) : const Duration(seconds: 1);
        await Future.delayed(checkInterval);
      }

      Logger.instance.warning('=== Cloudflare验证超时 ===', 'WebViewService');
      Logger.instance.warning('总检查次数: $checkCount, 是否曾在挑战状态: $wasInChallenge', 'WebViewService');
      return false;
    } catch (e) {
      Logger.instance.error('等待Cloudflare验证失败: $e', 'WebViewService');
      return false;
    }
  }
  
  /// 获取当前页面的认证令牌
  Future<Map<String, String>?> extractAuthTokens() async {
    try {
      if (_controller == null) return null;
      
      // 尝试从页面中提取常见的认证令牌
      final tokens = await _controller!.runJavaScriptReturningResult(
        '''
        (function() {
          var tokens = {};
          
          // 查找CSRF令牌
          var csrfMeta = document.querySelector('meta[name="csrf-token"]');
          if (csrfMeta) {
            tokens.csrf = csrfMeta.getAttribute('content');
          }
          
          // 查找其他认证令牌
          var authMeta = document.querySelector('meta[name="auth-token"]');
          if (authMeta) {
            tokens.auth = authMeta.getAttribute('content');
          }
          
          // 查找隐藏的表单字段
          var hiddenInputs = document.querySelectorAll('input[type="hidden"]');
          hiddenInputs.forEach(function(input) {
            if (input.name && input.value) {
              tokens[input.name] = input.value;
            }
          });
          
          return JSON.stringify(tokens);
        })();
        '''
      ) as String;
      
      if (tokens.isNotEmpty && tokens != 'null') {
        // 解析JSON字符串
        final Map<String, dynamic> parsedTokens = {};
        // 这里需要手动解析，因为WebView返回的是字符串
        // 简化处理，实际应用中可能需要更复杂的JSON解析
        
        Logger.instance.debug('提取到认证令牌: $tokens', 'WebViewService');
        return {'raw_tokens': tokens};
      }
      
      return null;
    } catch (e) {
      Logger.instance.error('提取认证令牌失败: $e', 'WebViewService');
      return null;
    }
  }
  
  /// 清理WebView数据
  Future<void> clearData() async {
    try {
      if (_controller == null) return;
      
      await _controller!.clearCache();
      await _controller!.clearLocalStorage();
      
      Logger.instance.info('WebView数据已清理', 'WebViewService');
    } catch (e) {
      Logger.instance.error('清理WebView数据失败: $e', 'WebViewService');
    }
  }
  
  /// 获取当前控制器
  WebViewController? get controller => _controller;
  
  /// 检查是否已初始化
  bool get isInitialized => _isInitialized;
}
