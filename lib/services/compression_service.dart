import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:es_compression/brotli.dart';
import '../utils/logger.dart';

/// 压缩响应处理服务
/// 用于解压缩gzip、br、zstd等格式的HTTP响应
class CompressionService {
  static CompressionService? _instance;
  static CompressionService get instance => _instance ??= CompressionService._();
  
  CompressionService._();
  
  /// 解压缩响应数据
  ///
  /// [data] 压缩的响应数据
  /// [encoding] 压缩编码类型 (gzip, deflate, br, zstd)
  /// [contentType] 内容类型，用于确定如何解析数据
  /// 返回解压缩后的字符串内容
  Future<String?> decompressResponse({
    required dynamic data,
    String? encoding,
    String? contentType,
  }) async {
    try {
      Logger.instance.debug('开始解压缩响应 - 编码: $encoding, 类型: $contentType', 'CompressionService');
      Logger.instance.debug('原始数据类型: ${data.runtimeType}', 'CompressionService');

      // 如果没有压缩编码，直接返回字符串
      if (encoding == null || encoding.isEmpty) {
        Logger.instance.debug('无压缩编码，直接返回字符串', 'CompressionService');
        return data?.toString();
      }

      // 转换为字节数组
      Uint8List bytes;

      if (data is String) {
        // 如果Dio返回的是字符串，但实际是压缩的二进制数据
        // 需要转换为字节数组
        Logger.instance.debug('数据是字符串类型，长度: ${data.length}', 'CompressionService');

        // 尝试将字符串转换为字节数组
        try {
          // 使用Latin-1编码来保持字节的完整性
          bytes = Uint8List.fromList(latin1.encode(data));
          Logger.instance.debug('成功将字符串转换为字节数组', 'CompressionService');
        } catch (e) {
          Logger.instance.error('字符串转字节数组失败: $e', 'CompressionService');
          return data;
        }
      } else if (data is List<int>) {
        bytes = Uint8List.fromList(data);
        Logger.instance.debug('数据是List<int>类型，长度: ${bytes.length}', 'CompressionService');
      } else if (data is Uint8List) {
        bytes = data;
        Logger.instance.debug('数据是Uint8List类型，长度: ${bytes.length}', 'CompressionService');
      } else {
        Logger.instance.warning('不支持的数据类型: ${data.runtimeType}', 'CompressionService');
        return data?.toString();
      }
      
      Logger.instance.debug('原始数据大小: ${bytes.length} 字节', 'CompressionService');
      
      // 根据编码类型解压缩
      List<int> decompressedBytes;
      
      switch (encoding?.toLowerCase()) {
        case 'gzip':
          decompressedBytes = await _decompressGzip(bytes);
          break;
        case 'deflate':
          decompressedBytes = await _decompressDeflate(bytes);
          break;
        case 'br':
          decompressedBytes = await _decompressBrotli(bytes);
          break;
        case 'zstd':
          // Zstandard压缩暂时不支持，返回原始数据
          Logger.instance.warning('Zstandard压缩暂不支持，返回原始数据', 'CompressionService');
          decompressedBytes = bytes;
          break;
        default:
          Logger.instance.debug('无压缩编码或未知编码，直接处理', 'CompressionService');
          decompressedBytes = bytes;
      }
      
      Logger.instance.debug('解压缩后数据大小: ${decompressedBytes.length} 字节', 'CompressionService');
      
      // 转换为字符串
      String result;
      try {
        result = utf8.decode(decompressedBytes);
        Logger.instance.debug('成功解码为UTF-8字符串', 'CompressionService');
      } catch (e) {
        // 如果UTF-8解码失败，尝试其他编码
        Logger.instance.warning('UTF-8解码失败，尝试Latin-1: $e', 'CompressionService');
        result = latin1.decode(decompressedBytes);
      }
      
      Logger.instance.debug('解压缩完成，结果长度: ${result.length} 字符', 'CompressionService');
      
      return result;
      
    } catch (e) {
      Logger.instance.error('解压缩响应失败: $e', 'CompressionService');
      return data?.toString();
    }
  }
  
  /// 解压缩Gzip数据
  Future<List<int>> _decompressGzip(Uint8List data) async {
    try {
      Logger.instance.debug('开始Gzip解压缩', 'CompressionService');
      final decompressed = gzip.decode(data);
      Logger.instance.debug('Gzip解压缩成功', 'CompressionService');
      return decompressed;
    } catch (e) {
      Logger.instance.error('Gzip解压缩失败: $e', 'CompressionService');
      rethrow;
    }
  }
  
  /// 解压缩Deflate数据
  Future<List<int>> _decompressDeflate(Uint8List data) async {
    try {
      Logger.instance.debug('开始Deflate解压缩', 'CompressionService');
      final decompressed = zlib.decode(data);
      Logger.instance.debug('Deflate解压缩成功', 'CompressionService');
      return decompressed;
    } catch (e) {
      Logger.instance.error('Deflate解压缩失败: $e', 'CompressionService');
      rethrow;
    }
  }

  /// 使用Brotli解压缩数据
  ///
  /// [bytes] 压缩的字节数组
  /// 返回解压缩后的字节数组
  Future<List<int>> _decompressBrotli(Uint8List bytes) async {
    try {
      Logger.instance.debug('开始Brotli解压缩，输入大小: ${bytes.length} 字节', 'CompressionService');

      // 尝试使用es_compression包进行Brotli解压缩
      try {
        final decompressed = brotli.decode(bytes);
        Logger.instance.debug('Brotli解压缩成功，输出大小: ${decompressed.length} 字节', 'CompressionService');
        return decompressed;
      } catch (e) {
        Logger.instance.warning('es_compression Brotli解压缩失败，可能是库未正确加载: $e', 'CompressionService');

        // 回退策略：返回原始字节数据，让上层处理
        // 在实际应用中，这可能意味着需要用户手动处理或使用其他方法
        Logger.instance.warning('使用回退策略：返回原始压缩数据', 'CompressionService');
        return bytes;
      }
    } catch (e) {
      Logger.instance.error('Brotli解压缩完全失败: $e', 'CompressionService');
      rethrow;
    }
  }

  /// 检测响应是否为HTML内容
  /// 
  /// [content] 响应内容
  /// [contentType] 内容类型头
  bool isHtmlContent(String content, String? contentType) {
    // 检查Content-Type头
    if (contentType?.toLowerCase().contains('text/html') == true) {
      return true;
    }
    
    // 检查内容是否包含HTML标签
    final lowerContent = content.toLowerCase().trim();
    return lowerContent.startsWith('<!doctype html') ||
           lowerContent.startsWith('<html') ||
           lowerContent.contains('<head>') ||
           lowerContent.contains('<body>') ||
           lowerContent.contains('<title>');
  }
  
  /// 检测是否为Cloudflare挑战页面
  /// 
  /// [content] 页面内容
  /// [statusCode] HTTP状态码
  /// [headers] 响应头
  bool isCloudflareChallenge(String content, int? statusCode, Map<String, dynamic>? headers) {
    Logger.instance.debug('检测Cloudflare挑战 - 状态码: $statusCode', 'CompressionService');
    
    // 检查状态码
    if (statusCode == 403 || statusCode == 503) {
      Logger.instance.debug('状态码表明可能是Cloudflare挑战', 'CompressionService');
    }
    
    // 检查响应头
    final server = headers?['server']?.toString().toLowerCase();
    final cfMitigated = headers?['cf-mitigated']?.toString().toLowerCase();
    
    if (server == 'cloudflare') {
      Logger.instance.info('检测到Cloudflare服务器', 'CompressionService');
      return true;
    }
    
    if (cfMitigated == 'challenge') {
      Logger.instance.info('检测到cf-mitigated: challenge', 'CompressionService');
      return true;
    }
    
    // 检查页面内容
    final lowerContent = content.toLowerCase();
    final cloudflareIndicators = [
      'cloudflare',
      'checking your browser',
      'ddos protection',
      'cf-browser-verification',
      'cf-wrapper',
      'please wait',
      'ray id',
      'just a moment',
      '_cf_chl_opt',
      'challenge-platform',
      'cf-challenge',
      'cf-under-attack',
    ];
    
    for (final indicator in cloudflareIndicators) {
      if (lowerContent.contains(indicator)) {
        Logger.instance.info('检测到Cloudflare挑战指标: $indicator', 'CompressionService');
        return true;
      }
    }
    
    return false;
  }
  
  /// 提取挑战页面的关键信息
  /// 
  /// [content] 挑战页面内容
  /// 返回包含挑战信息的Map
  Map<String, String> extractChallengeInfo(String content) {
    final info = <String, String>{};
    
    try {
      // 提取Ray ID
      final rayIdMatch = RegExp(r'ray id[:\s]*([a-f0-9]+)', caseSensitive: false).firstMatch(content);
      if (rayIdMatch != null) {
        info['rayId'] = rayIdMatch.group(1) ?? '';
      }
      
      // 提取挑战类型
      if (content.toLowerCase().contains('checking your browser')) {
        info['challengeType'] = 'browser_check';
      } else if (content.toLowerCase().contains('ddos protection')) {
        info['challengeType'] = 'ddos_protection';
      } else if (content.toLowerCase().contains('just a moment')) {
        info['challengeType'] = 'js_challenge';
      } else {
        info['challengeType'] = 'unknown';
      }
      
      // 提取页面标题
      final titleMatch = RegExp(r'<title[^>]*>([^<]+)</title>', caseSensitive: false).firstMatch(content);
      if (titleMatch != null) {
        info['title'] = titleMatch.group(1)?.trim() ?? '';
      }
      
      Logger.instance.debug('提取到挑战信息: $info', 'CompressionService');
      
    } catch (e) {
      Logger.instance.error('提取挑战信息失败: $e', 'CompressionService');
    }
    
    return info;
  }
}
