import 'dart:async';
import 'dart:collection';
import 'dart:math';
import 'package:dio/dio.dart';
import 'package:dio_cookie_manager/dio_cookie_manager.dart' as dio_cookie;
import 'package:flutter/foundation.dart';
import '../models/plate_number.dart';
import '../config/app_config.dart';
import '../enums/vehicle_type.dart';
import 'api_logger.dart';
import '../utils/logger.dart';
import 'cookie_manager.dart';
import 'browser_headers.dart';
import 'human_behavior.dart';
import 'webview_service.dart';
import 'compression_service.dart';

class ApiService {
  final Dio _dio = Dio();
  final int _maxRetries = 3;
  bool _isInitialized = false;
  bool _webViewFallbackEnabled = false;

  ApiService() {
    // 配置Dio
    _configureDio();
  }

  /// 初始化API服务（包含所有反机器人检测组件）
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // 初始化Cookie管理器
      await CookieManager.instance.initialize();

      // 初始化WebView服务
      await WebViewService.instance.initialize();

      // 配置Cookie拦截器
      _dio.interceptors.add(dio_cookie.CookieManager(CookieManager.instance.cookieJar!));

      // 预热会话 - 访问主页面获取初始Cookie
      await _warmupSession();

      _isInitialized = true;
      Logger.instance.info('API服务初始化完成，反机器人检测功能已启用', 'ApiService');
    } catch (e) {
      Logger.instance.error('API服务初始化失败: $e', 'ApiService');
      _isInitialized = false;
    }
  }

  /// 配置Dio实例
  void _configureDio() {
    // 设置基础配置
    _dio.options.connectTimeout = const Duration(seconds: 45); // 增加超时时间
    _dio.options.receiveTimeout = const Duration(seconds: 45);
    _dio.options.sendTimeout = const Duration(seconds: 45);

    // 设置更真实的默认配置
    _dio.options.followRedirects = true;
    _dio.options.maxRedirects = 5;
    _dio.options.validateStatus = (status) {
      return status != null && status >= 200 && status < 400;
    };

    // 仅在Debug模式下添加详细日志拦截器
    if (kDebugMode) {
      _dio.interceptors.add(LogInterceptor(
        requestBody: true,
        responseBody: true,
        requestHeader: true,
        responseHeader: true,
        error: true,
        logPrint: (obj) => Logger.instance.debug('[Dio] $obj', 'ApiService'),
      ));
    }

    // 添加重试拦截器和403错误处理
    _dio.interceptors.add(InterceptorsWrapper(
      onError: (error, handler) async {
        Logger.instance.warning('请求发生错误 - 状态码: ${error.response?.statusCode}, 类型: ${error.type}', 'ApiService');

        // 详细记录错误信息
        if (error.response != null) {
          final response = error.response!;
          Logger.instance.debug('错误响应头: ${response.headers.map}', 'ApiService');
          Logger.instance.debug('错误响应数据类型: ${response.data.runtimeType}', 'ApiService');

          // 处理压缩响应
          final contentEncoding = response.headers.value('content-encoding');
          final contentType = response.headers.value('content-type');

          if (contentEncoding != null) {
            Logger.instance.info('响应被压缩 ($contentEncoding)，尝试解压缩', 'ApiService');

            try {
              final decompressedContent = await CompressionService.instance.decompressResponse(
                data: response.data,
                encoding: contentEncoding,
                contentType: contentType,
              );

              if (decompressedContent != null) {
                Logger.instance.debug('解压缩成功，内容长度: ${decompressedContent.length}', 'ApiService');

                // 检查是否为Cloudflare挑战
                if (CompressionService.instance.isCloudflareChallenge(
                  decompressedContent,
                  response.statusCode,
                  response.headers.map
                )) {
                  Logger.instance.warning('解压缩后检测到Cloudflare挑战页面', 'ApiService');

                  // 提取挑战信息
                  final challengeInfo = CompressionService.instance.extractChallengeInfo(decompressedContent);
                  Logger.instance.info('挑战信息: $challengeInfo', 'ApiService');

                  // 处理Cloudflare挑战
                  await _handleCloudflareChallenge403(error, decompressedContent, handler);
                  return;
                }
              }
            } catch (e) {
              Logger.instance.error('解压缩响应失败: $e', 'ApiService');
            }
          }
        }

        // 检查是否是Cloudflare挑战（原有逻辑）
        if (_isCloudflareChallenge(error)) {
          Logger.instance.warning('通过原有逻辑检测到Cloudflare挑战，尝试WebView回退', 'ApiService');

          if (_webViewFallbackEnabled) {
            try {
              final success = await _handleCloudflareChallenge(error.requestOptions.uri.toString());
              if (success) {
                Logger.instance.info('WebView挑战处理成功，重新发送请求', 'ApiService');
                // 重新发送原始请求
                final response = await _dio.fetch(error.requestOptions);
                handler.resolve(response);
                return;
              }
            } catch (e) {
              Logger.instance.error('WebView回退处理失败: $e', 'ApiService');
            }
          }
        }

        handler.next(error);
      },
    ));
  }

  /// 并发查询多个车牌号码（带反机器人检测）
  ///
  /// [plates] 要查询的车牌号码列表
  /// [concurrency] 并发数量 (1-3)
  /// [vehicleType] 车辆类型
  /// [onProgress] 进度回调函数
  /// [onPlateCompleted] 单个车牌完成时的回调函数，用于实时保存
  /// [shouldCancel] 检查是否应该取消查询的函数
  Future<List<PlateNumber>> checkPlatesAvailability({
    required List<PlateNumber> plates,
    required int concurrency,
    required VehicleType vehicleType,
    required Function(int completed, int total) onProgress,
    Function(PlateNumber plate)? onPlateCompleted,
    bool Function()? shouldCancel,
  }) async {
    // 确保服务已初始化
    if (!_isInitialized) {
      await initialize();
    }

    Logger.instance.info('开始查询 ${plates.length} 个车牌号码（反机器人模式）', 'ApiService');

    // 验证并发数（降低并发以避免触发检测）
    final int actualConcurrency = concurrency.clamp(1, 2); // 最大并发降低到2
    Logger.instance.info('实际并发数: $actualConcurrency', 'ApiService');

    // 创建结果列表
    final List<PlateNumber> results = [];

    // 创建查询队列
    final queue = Queue<PlateNumber>.from(plates);
    int completed = 0;

    // 使用Completer来等待所有任务完成
    final completer = Completer<List<PlateNumber>>();

    // 处理下一个车牌
    void processNext() async {
      // 检查是否应该取消查询
      if (shouldCancel?.call() == true) {
        Logger.instance.info('查询被用户取消', 'ApiService');
        completer.complete(results);
        return;
      }

      if (queue.isEmpty) {
        // 如果队列为空且所有任务已完成，则完成Future
        if (completed == plates.length) {
          Logger.instance.info('所有查询任务已完成', 'ApiService');
          completer.complete(results);
        }
        return;
      }

      final plate = queue.removeFirst();
      Logger.instance.debug('正在查询车牌: ${plate.number}', 'ApiService');

      try {
        // 使用人性化行为执行查询
        final updatedPlate = await HumanBehavior.instance.executeWithDelay(
          () => _checkPlateWithRetry(plate, vehicleType, shouldCancel),
          operationId: 'plate_check_${plate.number}',
        );

        results.add(updatedPlate);
        Logger.instance.debug('车牌 ${plate.number} 查询结果: ${updatedPlate.isAvailable}', 'ApiService');

        // 调用单个车牌完成回调（实时保存）
        if (onPlateCompleted != null) {
          try {
            await onPlateCompleted(updatedPlate);
          } catch (e) {
            Logger.instance.error('保存车牌 ${plate.number} 到数据库时发生错误: $e', 'ApiService');
          }
        }

        // 更新进度
        completed++;
        onProgress(completed, plates.length);

        // 处理下一个
        processNext();
      } catch (e) {
        Logger.instance.error('查询车牌 ${plate.number} 时发生错误: $e', 'ApiService');
        // 处理错误，将失败的查询添加回队列或标记为失败
        final failedPlate = plate.copyWith(
          isAvailable: null,
          queryTime: DateTime.now(),
        );
        results.add(failedPlate);

        // 即使失败也要调用回调保存记录
        if (onPlateCompleted != null) {
          try {
            await onPlateCompleted(failedPlate);
          } catch (e) {
            Logger.instance.error('保存失败车牌 ${plate.number} 到数据库时发生错误: $e', 'ApiService');
          }
        }

        completed++;
        onProgress(completed, plates.length);
        processNext();
      }
    }

    // 启动并发任务
    for (int i = 0; i < actualConcurrency; i++) {
      processNext();
    }

    // 等待所有任务完成
    return completer.future;
  }

  /// 带重试机制的车牌查询
  Future<PlateNumber> _checkPlateWithRetry(PlateNumber plate, VehicleType vehicleType, [bool Function()? shouldCancel]) async {
    int attempts = 0;

    while (attempts < _maxRetries) {
      // 在重试前检查是否应该取消
      if (shouldCancel?.call() == true) {
        Logger.instance.info('查询车牌 ${plate.number} 被取消', 'ApiService');
        throw Exception('Query cancelled by user');
      }

      try {
        // 尝试查询
        return await _checkPlateAvailability(plate, vehicleType);
      } catch (e) {
        attempts++;
        Logger.instance.warning('第 $attempts 次重试查询车牌 ${plate.number}', 'ApiService');

        // 如果达到最大重试次数，抛出异常
        if (attempts >= _maxRetries) {
          Logger.instance.error('达到最大重试次数，放弃查询车牌 ${plate.number}', 'ApiService');
          rethrow;
        }

        // 计算退避时间 (指数增长: 500ms, 1000ms, 2000ms...)
        final backoffMs = (500 * pow(2, attempts - 1)).toInt();
        // 添加随机抖动，避免同时重试
        final jitter = Random().nextInt(200);
        final delayMs = backoffMs + jitter;

        Logger.instance.debug('等待 ${delayMs}ms 后重试', 'ApiService');

        // 在等待期间分段检查取消状态
        const checkInterval = 100; // 每100ms检查一次
        int elapsed = 0;
        while (elapsed < delayMs) {
          if (shouldCancel?.call() == true) {
            Logger.instance.info('在重试等待期间查询被取消', 'ApiService');
            throw Exception('Query cancelled by user');
          }
          final waitTime = (delayMs - elapsed).clamp(0, checkInterval);
          await Future.delayed(Duration(milliseconds: waitTime));
          elapsed += waitTime;
        }
      }
    }

    // 不应该到达这里，但为了类型安全
    throw Exception('Failed to check plate after $_maxRetries attempts');
  }

  /// 单个车牌查询（增强版）
  Future<PlateNumber> _checkPlateAvailability(PlateNumber plate, VehicleType vehicleType) async {
    final url = AppConfig.apiBaseUrl;
    final startTime = DateTime.now();

    Logger.instance.debug('请求URL: $url', 'ApiService');
    Logger.instance.debug('车辆类型: ${vehicleType.apiValue}', 'ApiService');

    try {
      // 获取增强的浏览器请求头
      final headers = await BrowserHeaders.instance.getBrowserHeaders(
        referer: AppConfig.refererUrl,
      );

      Logger.instance.debug('使用增强请求头: ${headers.length} 个字段', 'ApiService');

      // 准备请求参数（确保时间戳格式正确）
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final queryParameters = {
        'vehicleType': vehicleType.apiValue.toLowerCase(), // 使用小写格式，与浏览器一致
        'combination': plate.number,
        'productType': 'Create',
        'isRestyle': false,
        '_': timestamp,
      };

      Logger.instance.debug('请求参数: $queryParameters', 'ApiService');

      // 记录请求日志
      await ApiLogger.logRequest(
        method: 'GET',
        url: url,
        queryParameters: queryParameters,
        headers: headers,
        plateNumber: plate.number,
      );

      final response = await _dio.get(
        url,
        queryParameters: queryParameters,
        options: Options(
          headers: headers,
          validateStatus: (status) {
            return status != null && status >= 200 && status < 400;
          },
          followRedirects: true,
          maxRedirects: 5,
          // 添加额外的反检测选项
          extra: {
            'plate_number': plate.number,
            'request_time': startTime.toIso8601String(),
          },
        ),
      );

      final duration = DateTime.now().difference(startTime);

      Logger.instance.debug('响应状态码: ${response.statusCode}', 'ApiService');
      Logger.instance.debug('响应数据: ${response.data}', 'ApiService');

      // 记录响应日志
      await ApiLogger.logResponse(
        url: url,
        statusCode: response.statusCode,
        headers: response.headers.map,
        data: response.data,
        plateNumber: plate.number,
        duration: duration,
      );

      // 处理响应
      bool? isAvailable;

      // 处理响应数据
      final data = response.data;
      Logger.instance.debug('响应状态码: ${response.statusCode}', 'ApiService');
      Logger.instance.debug('响应数据类型: ${data.runtimeType}', 'ApiService');

      // 检查是否收到HTML响应（可能是Cloudflare挑战）
      if (data is String) {
        Logger.instance.debug('收到字符串响应，长度: ${data.length}', 'ApiService');

        // 使用压缩服务检查是否为HTML内容
        final isHtmlResponse = CompressionService.instance.isHtmlContent(data, response.headers.value('content-type'));

        if (isHtmlResponse || response.statusCode == 403) {
          Logger.instance.warning('收到HTML响应（状态码: ${response.statusCode}），可能是Cloudflare挑战', 'ApiService');
          Logger.instance.debug('响应内容预览: ${data.length > 200 ? data.substring(0, 200) + "..." : data}', 'ApiService');

          // 使用压缩服务检查是否为Cloudflare挑战
          if (CompressionService.instance.isCloudflareChallenge(data, response.statusCode, response.headers.map)) {
            Logger.instance.error('检测到Cloudflare挑战页面', 'ApiService');

            // 提取挑战信息
            final challengeInfo = CompressionService.instance.extractChallengeInfo(data);
            Logger.instance.info('挑战详情: $challengeInfo', 'ApiService');

            if (_webViewFallbackEnabled) {
              // 构建完整的挑战URL
              final challengeUrl = '${AppConfig.apiBaseUrl}?vehicleType=${vehicleType.apiValue.toLowerCase()}&combination=${plate.number}&productType=Create&isRestyle=false&_=${DateTime.now().millisecondsSinceEpoch}';

              Logger.instance.info('尝试使用WebView处理Cloudflare挑战: $challengeUrl', 'ApiService');
              throw DioException(
                requestOptions: response.requestOptions,
                response: response,
                type: DioExceptionType.badResponse,
                message: 'Cloudflare挑战需要WebView处理',
              );
            } else {
              throw Exception('检测到Cloudflare挑战，但WebView回退未启用');
            }
          }

          // 如果不是挑战页面，但仍然是HTML，说明可能是错误页面
          throw Exception('收到意外的HTML响应，而不是JSON数据');
        }
      }

      if (data is Map<String, dynamic>) {
        Logger.instance.debug('响应数据键: ${data.keys.toList()}', 'ApiService');

        // 尝试多种可能的响应格式
        if (data.containsKey('plateAvailability')) {
          final plateAvailability = data['plateAvailability'];
          Logger.instance.debug('plateAvailability: $plateAvailability', 'ApiService');

          if (plateAvailability is Map<String, dynamic>) {
            isAvailable = plateAvailability['available'] as bool?;
          } else if (plateAvailability is bool) {
            isAvailable = plateAvailability;
          }
        } else if (data.containsKey('available')) {
          isAvailable = data['available'] as bool?;
        } else if (data.containsKey('isAvailable')) {
          isAvailable = data['isAvailable'] as bool?;
        } else {
          // 如果找不到明确的可用性字段，检查是否有错误信息
          Logger.instance.warning('未找到可用性字段，响应数据: $data', 'ApiService');
          isAvailable = null; // 保持null以便调试
        }

        Logger.instance.debug('解析后的可用性: $isAvailable', 'ApiService');
      } else {
        Logger.instance.warning('警告: 响应数据格式不正确: ${data.runtimeType}', 'ApiService');
        Logger.instance.warning('响应内容: $data', 'ApiService');
        isAvailable = null;
      }

      // 返回更新后的车牌对象
      return plate.copyWith(
        isAvailable: isAvailable,
        queryTime: DateTime.now(),
      );
    } catch (e) {
      Logger.instance.error('查询出错: $e', 'ApiService');

      // 记录错误日志
      await ApiLogger.logError(
        url: url,
        error: e,
        plateNumber: plate.number,
        stackTrace: StackTrace.current,
      );

      // 处理网络错误或其他异常
      if (e is DioException) {
        Logger.instance.error('Dio错误类型: ${e.type}', 'ApiService');
        Logger.instance.error('Dio错误消息: ${e.message}', 'ApiService');
        Logger.instance.error('请求选项: ${e.requestOptions.uri}', 'ApiService');

        if (e.response != null) {
          Logger.instance.error('错误响应状态码: ${e.response!.statusCode}', 'ApiService');
          Logger.instance.error('错误响应数据: ${e.response!.data}', 'ApiService');
          Logger.instance.error('错误响应头: ${e.response!.headers}', 'ApiService');
        }
      }

      // 重新抛出异常以便重试机制处理
      rethrow;
    }
  }

  /// 检测是否为Cloudflare挑战
  bool _isCloudflareChallenge(DioException error) {
    if (error.response == null) return false;

    final statusCode = error.response!.statusCode;
    final responseData = error.response!.data?.toString() ?? '';

    Logger.instance.debug('检查Cloudflare挑战 - 状态码: $statusCode', 'ApiService');
    Logger.instance.debug('响应数据长度: ${responseData.length}', 'ApiService');

    // 检查状态码
    if (statusCode == 403 || statusCode == 503) {
      Logger.instance.debug('状态码表明可能是Cloudflare挑战', 'ApiService');
    }

    // 检查响应内容
    final cloudflareIndicators = [
      'cloudflare',
      'checking your browser',
      'ddos protection',
      'cf-browser-verification',
      'cf-wrapper',
      'please wait',
      'ray id',
      'just a moment',
      '_cf_chl_opt',
      'challenge-platform',
    ];

    final lowerResponseData = responseData.toLowerCase();
    for (final indicator in cloudflareIndicators) {
      if (lowerResponseData.contains(indicator)) {
        Logger.instance.info('检测到Cloudflare挑战指标: $indicator', 'ApiService');
        return true;
      }
    }

    return false;
  }

  /// 处理403错误中的Cloudflare挑战（增强版）
  Future<void> _handleCloudflareChallenge403(
    DioException error,
    String challengeContent,
    ErrorInterceptorHandler handler
  ) async {
    try {
      Logger.instance.info('=== 开始处理403 Cloudflare挑战 ===', 'ApiService');
      Logger.instance.info('挑战页面内容长度: ${challengeContent.length}', 'ApiService');

      if (!_webViewFallbackEnabled) {
        Logger.instance.warning('WebView回退未启用，无法处理挑战', 'ApiService');
        handler.next(error);
        return;
      }

      // 构建完整的挑战URL
      final originalUrl = error.requestOptions.uri.toString();
      Logger.instance.info('原始请求URL: $originalUrl', 'ApiService');

      // 记录挑战前的Cookie状态
      final cookieStatsBefore = await getCookieStats();
      Logger.instance.info('挑战前Cookie统计: $cookieStatsBefore', 'ApiService');

      // 使用WebView处理挑战
      final success = await _handleCloudflareChallenge(originalUrl);

      if (success) {
        Logger.instance.info('=== Cloudflare挑战处理成功 ===', 'ApiService');

        // 记录挑战后的Cookie状态
        final cookieStatsAfter = await getCookieStats();
        Logger.instance.info('挑战后Cookie统计: $cookieStatsAfter', 'ApiService');

        // 等待一小段时间确保Cookie生效
        await Future.delayed(const Duration(milliseconds: 1000));

        try {
          Logger.instance.info('重新发送原始请求', 'ApiService');
          final response = await _dio.fetch(error.requestOptions);
          Logger.instance.info('重试请求成功，状态码: ${response.statusCode}', 'ApiService');
          handler.resolve(response);
          return;
        } catch (retryError) {
          Logger.instance.error('重试请求失败: $retryError', 'ApiService');
          handler.next(error);
          return;
        }
      } else {
        Logger.instance.warning('=== Cloudflare挑战处理失败 ===', 'ApiService');
        handler.next(error);
        return;
      }

    } catch (e) {
      Logger.instance.error('处理403 Cloudflare挑战时发生错误: $e', 'ApiService');
      handler.next(error);
    }
  }

  /// 处理Cloudflare挑战（原有方法，保持兼容性）
  Future<bool> _handleCloudflareChallenge(String url) async {
    try {
      Logger.instance.info('开始处理Cloudflare挑战: $url', 'ApiService');

      // 创建WebView控制器
      final controller = WebViewService.instance.createController(
        initialUrl: url,
        onPageFinished: (url) {
          Logger.instance.debug('WebView页面加载完成: $url', 'ApiService');
        },
      );

      // 等待用户完成验证
      final success = await WebViewService.instance.waitForCloudflareVerification(
        timeout: const Duration(minutes: 3),
        onStatusUpdate: (status) {
          Logger.instance.info('Cloudflare验证状态: $status', 'ApiService');
        },
      );

      if (success) {
        Logger.instance.info('Cloudflare挑战处理成功', 'ApiService');
        return true;
      } else {
        Logger.instance.warning('Cloudflare挑战处理超时或失败', 'ApiService');
        return false;
      }
    } catch (e) {
      Logger.instance.error('处理Cloudflare挑战时发生错误: $e', 'ApiService');
      return false;
    }
  }

  /// 启用WebView回退模式
  void enableWebViewFallback() {
    _webViewFallbackEnabled = true;
    Logger.instance.info('WebView回退模式已启用', 'ApiService');
  }

  /// 禁用WebView回退模式
  void disableWebViewFallback() {
    _webViewFallbackEnabled = false;
    Logger.instance.info('WebView回退模式已禁用', 'ApiService');
  }

  /// 获取Cookie统计信息
  Future<Map<String, int>> getCookieStats() async {
    return await CookieManager.instance.getCookieStats();
  }

  /// 清除所有Cookie
  Future<void> clearCookies() async {
    await CookieManager.instance.clearAllCookies();
    BrowserHeaders.instance.clearCache();
    Logger.instance.info('已清除所有Cookie和缓存', 'ApiService');
  }

  /// 获取人性化行为统计
  Map<String, dynamic> getBehaviorStats() {
    return HumanBehavior.instance.getStats();
  }

  /// 重置人性化行为统计
  void resetBehaviorStats() {
    HumanBehavior.instance.reset();
  }

  /// 预热会话 - 访问主页面获取初始Cookie
  Future<void> _warmupSession() async {
    try {
      Logger.instance.info('开始预热会话，访问主页面获取Cookie', 'ApiService');

      // 获取浏览器请求头
      final headers = await BrowserHeaders.instance.getBrowserHeaders();

      // 访问主页面
      final response = await _dio.get(
        'https://vplates.com.au/create/check-combination',
        options: Options(
          headers: headers,
          validateStatus: (status) => status != null && status >= 200 && status < 400,
          followRedirects: true,
          maxRedirects: 5,
        ),
      );

      Logger.instance.info('预热会话完成，状态码: ${response.statusCode}', 'ApiService');

      // 等待一小段时间，模拟用户浏览行为
      await Future.delayed(const Duration(milliseconds: 1500));

    } catch (e) {
      Logger.instance.warning('预热会话失败，但继续初始化: $e', 'ApiService');
    }
  }

  /// 检查服务是否已初始化
  bool get isInitialized => _isInitialized;

  /// 检查WebView回退是否启用
  bool get isWebViewFallbackEnabled => _webViewFallbackEnabled;
}
